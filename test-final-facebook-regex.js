//ts-check
import FacebookReviewRegex from './facebook-review-regex.js';
import fs from 'fs';

async function testFinalFacebookRegex() {
  console.log('=== Testing Final Facebook Review Extraction ===\n');
  
  try {
    // Read the actual Facebook HTML file
    const htmlContent = await fs.promises.readFile('Fetched Facebook Review Page.html', 'utf8');
    console.log(`Loaded HTML file: ${htmlContent.length} characters`);
    
    // Test the new Facebook-specific extraction method
    console.log('\n1. Using extractFacebookReviews() method:');
    console.log('=========================================');
    
    const facebookReviews = FacebookReviewRegex.extractFacebookReviews(htmlContent);
    console.log(`Found ${facebookReviews.length} Facebook reviews`);
    
    if (facebookReviews.length > 0) {
      console.log('\nDetailed Facebook Reviews:');
      facebookReviews.slice(0, 5).forEach((review, index) => {
        console.log(`\n--- Review ${index + 1} ---`);
        console.log(`Author Name: ${review.author_name}`);
        console.log(`Author URL: ${review.author_url}`);
        console.log(`Author ID: ${review.author_id}`);
        console.log(`Rating: ${review.rating}`);
        console.log(`Recommended: ${review.recommended}`);
        console.log(`Time Posted: ${review.time_posted}`);
        console.log(`Time (Unix): ${review.time_posted_unix}`);
        console.log(`Time (Readable): ${FacebookReviewRegex.formatTimestamp(review.time_posted_unix)}`);
        console.log(`Review Text: ${review.review_text.substring(0, 200)}${review.review_text.length > 200 ? '...' : ''}`);
        console.log(`Source: ${review.source}`);
      });
      
      if (facebookReviews.length > 5) {
        console.log(`\n... and ${facebookReviews.length - 5} more reviews`);
      }
    }
    
    // Compare with general extraction method
    console.log('\n2. Comparing with general extractReviews() method:');
    console.log('==================================================');
    
    const generalReviews = FacebookReviewRegex.extractReviews(htmlContent);
    console.log(`General method found: ${generalReviews.length} reviews`);
    console.log(`Facebook-specific method found: ${facebookReviews.length} reviews`);
    
    // Test Unicode decoding specifically
    console.log('\n3. Testing Unicode decoding on sample texts:');
    console.log('=============================================');
    
    const unicodeTests = [
      '100\\u0025 recommend (220 reviews)',
      '\\u0627\\u0644\\u0633\\u0644\\u0627\\u0645 \\u0639\\u0644\\u06cc\\u06a9\\u0645',
      '\\u0645\\u0646\\u0648\\u0631 \\u0628\\u06a9 \\u0633\\u0679\\u0648\\u0631'
    ];
    
    unicodeTests.forEach((text, index) => {
      const decoded = FacebookReviewRegex.decodeUnicodeText(text);
      console.log(`\nTest ${index + 1}:`);
      console.log(`  Original: ${text}`);
      console.log(`  Decoded:  ${decoded}`);
    });
    
    // Extract and display specific review data
    console.log('\n4. Extracting specific review components:');
    console.log('=========================================');
    
    // Extract reviewer names
    const names = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewerNameRegex());
    console.log(`\nReviewer Names (${names.length} found):`);
    names.slice(0, 10).forEach((name, i) => console.log(`  ${i + 1}. ${name}`));
    
    // Extract profile URIs
    const uris = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getProfileUriRegex());
    const validUris = uris.filter(uri => uri.includes('facebook.com') && !uri.includes('login') && !uri.includes('recover'));
    console.log(`\nValid Profile URIs (${validUris.length} found):`);
    validUris.slice(0, 5).forEach((uri, i) => console.log(`  ${i + 1}. ${uri}`));
    
    // Extract timestamps
    const times = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getTimePostedRegex());
    console.log(`\nTimestamps (${times.length} found):`);
    times.slice(0, 5).forEach((time, i) => {
      const readable = FacebookReviewRegex.formatTimestamp(time);
      console.log(`  ${i + 1}. ${time} (${readable})`);
    });
    
    // Extract recommendations
    const ratings = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getRatingRegex());
    console.log(`\nRatings/Recommendations (${ratings.length} found):`);
    ratings.slice(0, 10).forEach((rating, i) => console.log(`  ${i + 1}. ${rating}`));
    
    // Extract review texts
    const texts = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewTextRegex());
    const validTexts = texts.filter(text => FacebookReviewRegex.isValidReviewText(text));
    console.log(`\nValid Review Texts (${validTexts.length} found):`);
    validTexts.slice(0, 3).forEach((text, i) => {
      const cleaned = FacebookReviewRegex.cleanText(text);
      console.log(`  ${i + 1}. ${cleaned.substring(0, 150)}${cleaned.length > 150 ? '...' : ''}`);
    });
    
    // Final summary
    console.log('\n5. Final Summary:');
    console.log('=================');
    console.log(`✓ Successfully extracted ${facebookReviews.length} complete Facebook reviews`);
    console.log(`✓ Found ${names.length} reviewer names`);
    console.log(`✓ Found ${validUris.length} valid profile URIs`);
    console.log(`✓ Found ${times.length} timestamps`);
    console.log(`✓ Found ${ratings.length} ratings/recommendations`);
    console.log(`✓ Found ${validTexts.length} valid review texts`);
    console.log(`✓ Unicode decoding working for Arabic/Urdu text`);
    
    // Create a sample output for integration
    console.log('\n6. Sample JSON output for integration:');
    console.log('======================================');
    
    const sampleOutput = {
      status: 'SUCCESS',
      total_reviews: facebookReviews.length,
      reviews: facebookReviews.slice(0, 3).map(review => ({
        author_name: review.author_name,
        author_url: review.author_url,
        rating: parseFloat(review.rating),
        time_posted: review.time_posted,
        review_text: review.review_text,
        recommended: review.recommended,
        language: /[\u0600-\u06FF]/.test(review.review_text) ? 'ar' : 'en' // Detect Arabic/Urdu
      }))
    };
    
    console.log(JSON.stringify(sampleOutput, null, 2));
    
  } catch (error) {
    console.error('Error testing Facebook regex:', error);
  }
}

// Run the test
await testFinalFacebookRegex();
