//ts-check

/**
 * Facebook Reviews Regex Patterns
 * Comprehensive regex patterns to extract reviewer information from Facebook pages
 */

export class FacebookReviewRegex {
  
  /**
   * Extract reviewer name from various Facebook HTML patterns
   */
  static getReviewerNameRegex() {
    return [
      // Pattern 1: Profile name in aria-label for reviews
      /aria-label="([^"]+)\s+(?:reviewed|rated|recommends)/gi,

      // Pattern 2: Profile link with review context
      /<a[^>]*href="[^"]*facebook\.com[^"]*"[^>]*>([^<]+)<\/a>[\s\S]{0,200}(?:review|rating|recommend)/gi,

      // Pattern 3: Name in review data structure (more specific)
      /"reviewer":\s*{\s*"name":\s*"([^"]+)"/g,

      // Pattern 4: Facebook review author pattern
      /data-testid="[^"]*author[^"]*"[^>]*>([^<]+)</gi,

      // Pattern 5: Review author in structured format
      /"author":\s*"([^"]+)"/g,

      // Pattern 6: Name followed by review indicators
      /<(?:span|div)[^>]*>([A-Z][a-z]+\s+[A-Z][a-z]+)<\/(?:span|div)>[\s\S]{0,100}(?:★|⭐|stars?|review)/gi
    ];
  }

  /**
   * Extract profile URI/URL patterns
   */
  static getProfileUriRegex() {
    return [
      // Pattern 1: Standard Facebook profile URLs
      /https?:\/\/(?:www\.)?facebook\.com\/(?:profile\.php\?id=\d+|[a-zA-Z0-9.]+)/g,
      
      // Pattern 2: Profile URLs in href attributes
      /href="(https?:\/\/(?:www\.)?facebook\.com\/[^"]+)"/g,
      
      // Pattern 3: Profile URLs in data attributes
      /data-href="(https?:\/\/(?:www\.)?facebook\.com\/[^"]+)"/g,
      
      // Pattern 4: Encoded profile URLs
      /https?%3A%2F%2F(?:www\.)?facebook\.com%2F[^"&\s]+/g,
      
      // Pattern 5: Profile ID in Facebook's internal format
      /"profile_id":"(\d+)"/g,
      
      // Pattern 6: User ID in various formats
      /"user_id":"?(\d+)"?/g
    ];
  }

  /**
   * Extract time posted patterns
   */
  static getTimePostedRegex() {
    return [
      // Pattern 1: Relative time descriptions
      /(\d+)\s+(minute|hour|day|week|month|year)s?\s+ago/gi,
      
      // Pattern 2: Specific dates
      /(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}/gi,
      
      // Pattern 3: Short date formats
      /\d{1,2}\/\d{1,2}\/\d{2,4}/g,
      
      // Pattern 4: ISO date format
      /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/g,
      
      // Pattern 5: Unix timestamp
      /"time[^"]*":\s*(\d{10,13})/g,
      
      // Pattern 6: Facebook's internal time format
      /"created_time":"([^"]+)"/g,
      
      // Pattern 7: Relative time in data attributes
      /data-time="([^"]+)"/g
    ];
  }

  /**
   * Extract rating/recommendation patterns
   */
  static getRatingRegex() {
    return [
      // Pattern 1: Star ratings (1-5 stars)
      /(\d)\s*(?:out of|\/)\s*5\s*stars?/gi,
      
      // Pattern 2: Numeric ratings
      /"rating":\s*(\d+(?:\.\d+)?)/g,
      
      // Pattern 3: Recommended/Not recommended
      /(recommends?|does not recommend|not recommended)/gi,
      
      // Pattern 4: Thumbs up/down
      /(thumbs?\s*up|thumbs?\s*down|like|dislike)/gi,
      
      // Pattern 5: Star symbols
      /★{1,5}|⭐{1,5}/g,
      
      // Pattern 6: Rating in aria-label
      /aria-label="[^"]*(\d)\s*(?:out of|\/)\s*5[^"]*"/gi,
      
      // Pattern 7: Facebook's internal rating format
      /"score":\s*(\d+(?:\.\d+)?)/g
    ];
  }

  /**
   * Extract review description/text patterns
   */
  static getReviewTextRegex() {
    return [
      // Pattern 1: Review text in structured data
      /"review_text":"([^"]+)"/g,

      // Pattern 2: Meaningful review content (longer text)
      /<(?:p|div|span)[^>]*>([^<]{20,500})<\/(?:p|div|span)>/g,

      // Pattern 3: Review text with context indicators
      /<(?:p|div|span)[^>]*>([^<]*(?:great|good|bad|terrible|excellent|amazing|love|hate|recommend)[^<]*)<\/(?:p|div|span)>/gi,

      // Pattern 4: Text following rating patterns
      /(?:★{1,5}|⭐{1,5}|\d\s*(?:stars?|\/5))[\s\S]{0,50}<[^>]*>([^<]{10,300})</g,

      // Pattern 5: Review content in JSON
      /"(?:text|content|review|comment)":\s*"([^"]{10,500})"/g,

      // Pattern 6: Text in review containers
      /data-testid="[^"]*(?:review|comment)[^"]*"[^>]*>[\s\S]*?<[^>]*>([^<]{15,400})</gi,

      // Pattern 7: Sentences with review keywords
      /([^.!?]*(?:service|food|place|experience|staff|quality|recommend|visit)[^.!?]*[.!?])/gi
    ];
  }

  /**
   * Comprehensive review extraction pattern
   * Attempts to match complete review blocks
   */
  static getCompleteReviewRegex() {
    return [
      // Pattern 1: Complete review block with name, rating, time, and text
      /<div[^>]*review[^>]*>[\s\S]*?<a[^>]*>([^<]+)<\/a>[\s\S]*?(\d)\s*(?:stars?|\/5)[\s\S]*?(\d+\s+\w+\s+ago)[\s\S]*?<(?:p|span)[^>]*>([^<]+)<\/(?:p|span)>[\s\S]*?<\/div>/gi,
      
      // Pattern 2: JSON-like structure in script tags
      /"reviews":\s*\[([\s\S]*?)\]/g,
      
      // Pattern 3: Facebook's internal review data structure
      /\[\[null,\[null,null,null,\[null,null,null,(\d+)\],null,\[null,null,null,null,null,\["([^"]+)"/g
    ];
  }

  /**
   * Extract all review data using multiple patterns
   * @param {string} htmlContent - The HTML content to parse
   * @returns {Array} Array of review objects
   */
  static extractReviews(htmlContent) {
    const reviews = [];

    // Try to extract using complete review patterns first
    const completePatterns = this.getCompleteReviewRegex();

    for (const pattern of completePatterns) {
      let match;
      while ((match = pattern.exec(htmlContent)) !== null) {
        const review = {
          author_name: match[1] || match[2] || '',
          rating: match[2] || match[1] || '',
          time_posted: match[3] || '',
          review_text: match[4] || match[3] || '',
          raw_match: match[0]
        };

        if (this.isValidReview(review)) {
          reviews.push(review);
        }
      }
    }

    // If no complete matches, try individual patterns
    if (reviews.length === 0) {
      const names = this.extractPattern(htmlContent, this.getReviewerNameRegex());
      const ratings = this.extractPattern(htmlContent, this.getRatingRegex());
      const times = this.extractPattern(htmlContent, this.getTimePostedRegex());
      const texts = this.extractPattern(htmlContent, this.getReviewTextRegex());
      const uris = this.extractPattern(htmlContent, this.getProfileUriRegex());

      // Filter out obvious non-review content
      const filteredNames = names.filter(name => this.isValidReviewerName(name));
      const filteredTexts = texts.filter(text => this.isValidReviewText(text));

      // Combine extracted data
      const maxLength = Math.max(filteredNames.length, ratings.length, times.length, filteredTexts.length);

      for (let i = 0; i < maxLength; i++) {
        const review = {
          author_name: filteredNames[i] || '',
          author_url: uris[i] || '',
          rating: ratings[i] || '',
          time_posted: times[i] || '',
          review_text: filteredTexts[i] || '',
          recommended: this.isRecommended(ratings[i] || '', filteredTexts[i] || '')
        };

        if (this.isValidReview(review)) {
          reviews.push(review);
        }
      }
    }

    return reviews.filter(review =>
      review.author_name || review.review_text || review.rating
    );
  }

  /**
   * Extract patterns using multiple regex patterns
   * @param {string} content - Content to search
   * @param {Array} patterns - Array of regex patterns
   * @returns {Array} Array of matches
   */
  static extractPattern(content, patterns) {
    const matches = [];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        if (match[1]) {
          matches.push(match[1]);
        }
      }
    }
    
    return [...new Set(matches)]; // Remove duplicates
  }

  /**
   * Determine if a review is recommended based on rating and text
   * @param {string} rating - The rating value
   * @param {string} text - The review text
   * @returns {boolean} Whether the review is recommended
   */
  static isRecommended(rating, text) {
    // Check numeric rating
    const numRating = parseFloat(rating);
    if (!isNaN(numRating)) {
      return numRating >= 3; // 3+ stars is considered recommended
    }
    
    // Check text for recommendation keywords
    const lowerText = text.toLowerCase();
    const positiveKeywords = ['recommend', 'great', 'excellent', 'amazing', 'love', 'perfect'];
    const negativeKeywords = ['not recommend', 'terrible', 'awful', 'hate', 'worst'];
    
    const hasPositive = positiveKeywords.some(keyword => lowerText.includes(keyword));
    const hasNegative = negativeKeywords.some(keyword => lowerText.includes(keyword));
    
    if (hasNegative) return false;
    if (hasPositive) return true;
    
    return null; // Unknown
  }

  /**
   * Validate if extracted content is a real review
   * @param {Object} review - Review object to validate
   * @returns {boolean} Whether the review is valid
   */
  static isValidReview(review) {
    // Must have either author name or review text
    if (!review.author_name && !review.review_text) return false;

    // Check if author name looks like a real name
    if (review.author_name && !this.isValidReviewerName(review.author_name)) return false;

    // Check if review text looks like actual review content
    if (review.review_text && !this.isValidReviewText(review.review_text)) return false;

    return true;
  }

  /**
   * Validate if a name looks like a real reviewer name
   * @param {string} name - Name to validate
   * @returns {boolean} Whether the name is valid
   */
  static isValidReviewerName(name) {
    if (!name || name.length < 2) return false;

    // Filter out obvious non-names
    const invalidPatterns = [
      /^(log in|sign up|home|about|contact|menu|search|posts|photos|videos|reviews)$/i,
      /bundle|worker|entrypoint|script|function|component/i,
      /^[0-9]+$/,
      /^[^a-zA-Z]*$/,
      /facebook|meta|instagram|whatsapp/i
    ];

    return !invalidPatterns.some(pattern => pattern.test(name));
  }

  /**
   * Validate if text looks like actual review content
   * @param {string} text - Text to validate
   * @returns {boolean} Whether the text is valid
   */
  static isValidReviewText(text) {
    if (!text || text.length < 10) return false;

    // Filter out obvious non-review content
    const invalidPatterns = [
      /^(log in|sign up|home|about|contact|menu|search|posts|photos|videos|reviews)$/i,
      /bundle|worker|entrypoint|script|function|component/i,
      /^[0-9\s\-_]+$/,
      /facebook|meta|instagram|whatsapp/i,
      /^(see more|show less|like|comment|share)$/i
    ];

    // Must contain some review-like content
    const reviewKeywords = [
      /\b(good|bad|great|excellent|terrible|amazing|awful|love|hate|recommend|service|food|place|experience|staff|quality|visit|customer|price|clean|dirty|friendly|rude)\b/i
    ];

    const hasInvalidContent = invalidPatterns.some(pattern => pattern.test(text));
    const hasReviewContent = reviewKeywords.some(pattern => pattern.test(text));

    return !hasInvalidContent && (hasReviewContent || text.length > 50);
  }

  /**
   * Clean and normalize extracted text
   * @param {string} text - Text to clean
   * @returns {string} Cleaned text
   */
  static cleanText(text) {
    if (!text) return '';

    return text
      .replace(/\\n/g, ' ')
      .replace(/\\"/g, '"')
      .replace(/\s+/g, ' ')
      .trim();
  }
}

// Export for use in other modules
export default FacebookReviewRegex;
