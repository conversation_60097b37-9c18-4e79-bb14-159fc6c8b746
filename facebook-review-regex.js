//ts-check

/**
 * Facebook Reviews Regex Patterns
 * Comprehensive regex patterns to extract reviewer information from Facebook pages
 */

export class FacebookReviewRegex {
  
  /**
   * Extract reviewer name from various Facebook HTML patterns
   */
  static getReviewerNameRegex() {
    return [
      // Pattern 1: Facebook JSON structure - actor name
      /"actors":\s*\[\s*{[^}]*"name":\s*"([^"]+)"/g,

      // Pattern 2: Facebook JSON structure - user name
      /"name":\s*"([^"]+)"[^}]*"__typename":\s*"User"/g,

      // Pattern 3: Profile name in aria-label for reviews
      /aria-label="([^"]+)\s+(?:reviewed|rated|recommends)/gi,

      // Pattern 4: Profile link with review context
      /<a[^>]*href="[^"]*facebook\.com[^"]*"[^>]*>([^<]+)<\/a>[\s\S]{0,200}(?:review|rating|recommend)/gi,

      // Pattern 5: Facebook's internal user structure
      /"__typename":\s*"User"[^}]*"name":\s*"([^"]+)"/g,

      // Pattern 6: Name in title text with recommends
      /"text":\s*"([^"]+)\s+recommends/g,

      // Pattern 7: Name followed by review indicators
      /<(?:span|div)[^>]*>([A-Z][a-z]+\s+[A-Z][a-z]+)<\/(?:span|div)>[\s\S]{0,100}(?:★|⭐|stars?|review)/gi
    ];
  }

  /**
   * Extract profile URI/URL patterns
   */
  static getProfileUriRegex() {
    return [
      // Pattern 1: Facebook JSON structure - profile URL
      /"url":\s*"(https?:\/\/(?:web\.)?facebook\.com\/[^"]+)"/g,

      // Pattern 2: Facebook JSON structure - profile_url
      /"profile_url":\s*"(https?:\/\/(?:web\.)?facebook\.com\/[^"]+)"/g,

      // Pattern 3: Standard Facebook profile URLs in href
      /href="(https?:\/\/(?:web\.)?facebook\.com\/[^"]+)"/g,

      // Pattern 4: Facebook user ID in JSON
      /"id":\s*"(\d{10,})"/g,

      // Pattern 5: Encoded profile URLs
      /https?%3A%2F%2F(?:web\.)?facebook\.com%2F[^"&\s]+/g,

      // Pattern 6: Mobile Facebook URLs
      /"mobileUrl":\s*"(https?:\/\/m\.facebook\.com\/[^"]+)"/g
    ];
  }

  /**
   * Extract time posted patterns
   */
  static getTimePostedRegex() {
    return [
      // Pattern 1: Facebook JSON structure - creation_time (Unix timestamp)
      /"creation_time":\s*(\d{10,13})/g,

      // Pattern 2: Facebook JSON structure - publish_time
      /"publish_time":\s*(\d{10,13})/g,

      // Pattern 3: Relative time descriptions
      /(\d+)\s+(minute|hour|day|week|month|year)s?\s+ago/gi,

      // Pattern 4: Specific dates
      /(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}/gi,

      // Pattern 5: Short date formats
      /\d{1,2}\/\d{1,2}\/\d{2,4}/g,

      // Pattern 6: ISO date format
      /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/g,

      // Pattern 7: Relative time in data attributes
      /data-time="([^"]+)"/g
    ];
  }

  /**
   * Extract rating/recommendation patterns
   */
  static getRatingRegex() {
    return [
      // Pattern 1: Facebook JSON structure - recommends text
      /"text":\s*"[^"]*\s+(recommends?)\s+[^"]*"/gi,

      // Pattern 2: Facebook JSON structure - reaction count
      /"reaction_count":\s*{\s*"count":\s*(\d+)/g,

      // Pattern 3: Star ratings (1-5 stars)
      /(\d)\s*(?:out of|\/)\s*5\s*stars?/gi,

      // Pattern 4: Numeric ratings
      /"rating":\s*(\d+(?:\.\d+)?)/g,

      // Pattern 5: Recommended/Not recommended
      /(recommends?|does not recommend|not recommended)/gi,

      // Pattern 6: Star symbols
      /★{1,5}|⭐{1,5}/g,

      // Pattern 7: Rating in aria-label
      /aria-label="[^"]*(\d)\s*(?:out of|\/)\s*5[^"]*"/gi,

      // Pattern 8: Facebook's internal rating format
      /"score":\s*(\d+(?:\.\d+)?)/g
    ];
  }

  /**
   * Extract review description/text patterns
   */
  static getReviewTextRegex() {
    return [
      // Pattern 1: Facebook JSON structure - message text with Unicode
      /"text":\s*"([^"]*\\u[^"]+)"/g,

      // Pattern 2: Facebook JSON structure - regular message text
      /"message":\s*{\s*[^}]*"text":\s*"([^"]{10,1000})"/g,

      // Pattern 3: Facebook JSON structure - TextWithEntities
      /"__typename":\s*"TextWithEntities"[^}]*"text":\s*"([^"]{10,1000})"/g,

      // Pattern 4: Review text in structured data
      /"review_text":"([^"]+)"/g,

      // Pattern 5: Meaningful review content (longer text)
      /<(?:p|div|span)[^>]*>([^<]{20,500})<\/(?:p|div|span)>/g,

      // Pattern 6: Review text with context indicators
      /<(?:p|div|span)[^>]*>([^<]*(?:great|good|bad|terrible|excellent|amazing|love|hate|recommend)[^<]*)<\/(?:p|div|span)>/gi,

      // Pattern 7: Review content in JSON
      /"(?:content|review|comment)":\s*"([^"]{10,500})"/g,

      // Pattern 8: Sentences with review keywords
      /([^.!?]*(?:service|food|place|experience|staff|quality|recommend|visit)[^.!?]*[.!?])/gi
    ];
  }

  /**
   * Comprehensive review extraction pattern
   * Attempts to match complete review blocks
   */
  static getCompleteReviewRegex() {
    return [
      // Pattern 1: Complete review block with name, rating, time, and text
      /<div[^>]*review[^>]*>[\s\S]*?<a[^>]*>([^<]+)<\/a>[\s\S]*?(\d)\s*(?:stars?|\/5)[\s\S]*?(\d+\s+\w+\s+ago)[\s\S]*?<(?:p|span)[^>]*>([^<]+)<\/(?:p|span)>[\s\S]*?<\/div>/gi,
      
      // Pattern 2: JSON-like structure in script tags
      /"reviews":\s*\[([\s\S]*?)\]/g,
      
      // Pattern 3: Facebook's internal review data structure
      /\[\[null,\[null,null,null,\[null,null,null,(\d+)\],null,\[null,null,null,null,null,\["([^"]+)"/g
    ];
  }

  /**
   * Extract all review data using multiple patterns
   * @param {string} htmlContent - The HTML content to parse
   * @returns {Array} Array of review objects
   */
  static extractReviews(htmlContent) {
    const reviews = [];

    // Try to extract using complete review patterns first
    const completePatterns = this.getCompleteReviewRegex();

    for (const pattern of completePatterns) {
      let match;
      while ((match = pattern.exec(htmlContent)) !== null) {
        const review = {
          author_name: match[1] || match[2] || '',
          rating: match[2] || match[1] || '',
          time_posted: match[3] || '',
          review_text: match[4] || match[3] || '',
          raw_match: match[0]
        };

        if (this.isValidReview(review)) {
          reviews.push(review);
        }
      }
    }

    // If no complete matches, try individual patterns
    if (reviews.length === 0) {
      const names = this.extractPattern(htmlContent, this.getReviewerNameRegex());
      const ratings = this.extractPattern(htmlContent, this.getRatingRegex());
      const times = this.extractPattern(htmlContent, this.getTimePostedRegex());
      const texts = this.extractPattern(htmlContent, this.getReviewTextRegex());
      const uris = this.extractPattern(htmlContent, this.getProfileUriRegex());

      // Filter out obvious non-review content
      const filteredNames = names.filter(name => this.isValidReviewerName(name));
      const filteredTexts = texts.filter(text => this.isValidReviewText(text));

      // Combine extracted data
      const maxLength = Math.max(filteredNames.length, ratings.length, times.length, filteredTexts.length);

      for (let i = 0; i < maxLength; i++) {
        const review = {
          author_name: filteredNames[i] || '',
          author_url: uris[i] || '',
          rating: ratings[i] || '',
          time_posted: times[i] || '',
          review_text: filteredTexts[i] || '',
          recommended: this.isRecommended(ratings[i] || '', filteredTexts[i] || '')
        };

        if (this.isValidReview(review)) {
          reviews.push(review);
        }
      }
    }

    const filteredReviews = reviews.filter(review =>
      review.author_name || review.review_text || review.rating
    );

    // Filter duplicates and empty names before returning
    return this.filterDuplicateReviews(filteredReviews);
  }

  /**
   * Extract patterns using multiple regex patterns
   * @param {string} content - Content to search
   * @param {Array} patterns - Array of regex patterns
   * @returns {Array} Array of matches
   */
  static extractPattern(content, patterns) {
    const matches = [];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        if (match[1]) {
          matches.push(match[1]);
        }
      }
    }
    
    return [...new Set(matches)]; // Remove duplicates
  }

  /**
   * Determine if a review is recommended based on rating and text
   * @param {string} rating - The rating value
   * @param {string} text - The review text
   * @returns {boolean} Whether the review is recommended
   */
  static isRecommended(rating, text) {
    // Check numeric rating
    const numRating = parseFloat(rating);
    if (!isNaN(numRating)) {
      return numRating >= 3; // 3+ stars is considered recommended
    }
    
    // Check text for recommendation keywords
    const lowerText = text.toLowerCase();
    const positiveKeywords = ['recommend', 'great', 'excellent', 'amazing', 'love', 'perfect'];
    const negativeKeywords = ['not recommend', 'terrible', 'awful', 'hate', 'worst'];
    
    const hasPositive = positiveKeywords.some(keyword => lowerText.includes(keyword));
    const hasNegative = negativeKeywords.some(keyword => lowerText.includes(keyword));
    
    if (hasNegative) return false;
    if (hasPositive) return true;
    
    return null; // Unknown
  }

  /**
   * Validate if extracted content is a real review
   * @param {Object} review - Review object to validate
   * @returns {boolean} Whether the review is valid
   */
  static isValidReview(review) {
    // Must have either author name or review text
    if (!review.author_name && !review.review_text) return false;

    // Check if author name looks like a real name
    if (review.author_name && !this.isValidReviewerName(review.author_name)) return false;

    // Check if review text looks like actual review content
    if (review.review_text && !this.isValidReviewText(review.review_text)) return false;

    return true;
  }

  /**
   * Validate if a name looks like a real reviewer name
   * @param {string} name - Name to validate
   * @returns {boolean} Whether the name is valid
   */
  static isValidReviewerName(name) {
    if (!name || name.length < 2) return false;

    // Filter out obvious non-names
    const invalidPatterns = [
      /^(log in|sign up|home|about|contact|menu|search|posts|photos|videos|reviews)$/i,
      /bundle|worker|entrypoint|script|function|component/i,
      /^[0-9]+$/,
      /^[^a-zA-Z]*$/,
      /facebook|meta|instagram|whatsapp/i
    ];

    return !invalidPatterns.some(pattern => pattern.test(name));
  }

  /**
   * Validate if text looks like actual review content
   * @param {string} text - Text to validate
   * @returns {boolean} Whether the text is valid
   */
  static isValidReviewText(text) {
    if (!text || text.length < 10) return false;

    // Filter out obvious non-review content
    const invalidPatterns = [
      /^(log in|sign up|home|about|contact|menu|search|posts|photos|videos|reviews)$/i,
      /bundle|worker|entrypoint|script|function|component/i,
      /^[0-9\s\-_]+$/,
      /facebook|meta|instagram|whatsapp/i,
      /^(see more|show less|like|comment|share)$/i
    ];

    // Must contain some review-like content
    const reviewKeywords = [
      /\b(good|bad|great|excellent|terrible|amazing|awful|love|hate|recommend|service|food|place|experience|staff|quality|visit|customer|price|clean|dirty|friendly|rude)\b/i
    ];

    const hasInvalidContent = invalidPatterns.some(pattern => pattern.test(text));
    const hasReviewContent = reviewKeywords.some(pattern => pattern.test(text));

    return !hasInvalidContent && (hasReviewContent || text.length > 50);
  }

  /**
   * Decode Facebook's Unicode escape sequences
   * @param {string} text - Text with Unicode escapes
   * @returns {string} Decoded text
   */
  static decodeUnicodeText(text) {
    if (!text) return '';

    try {
      // Decode Unicode escape sequences like \u0645\u0646\u0648\u0631
      let decoded = text.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
        return String.fromCharCode(parseInt(code, 16));
      });

      // Also handle other common escape sequences
      decoded = decoded
        .replace(/\\n/g, '\n')
        .replace(/\\r/g, '\r')
        .replace(/\\t/g, '\t')
        .replace(/\\"/g, '"')
        .replace(/\\\\/g, '\\');

      return decoded;
    } catch (error) {
      return text;
    }
  }

  /**
   * Clean and normalize extracted text
   * @param {string} text - Text to clean
   * @returns {string} Cleaned text
   */
  static cleanText(text) {
    if (!text) return '';

    // First decode Unicode sequences
    let cleaned = this.decodeUnicodeText(text);

    return cleaned
      .replace(/\\n/g, ' ')
      .replace(/\\"/g, '"')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Extract Facebook reviews using the specific JSON structure found in the HTML
   * @param {string} htmlContent - The HTML content to parse
   * @returns {Array} Array of review objects
   */
  static extractFacebookReviews(htmlContent) {
    const reviews = [];
    const seenReviews = new Set(); // Track duplicates

    // Pattern 1: Facebook's specific review structure with actors and message
    const facebookReviewPattern1 = /"actors":\s*\[\s*{\s*"__typename":\s*"User",\s*"id":\s*"([^"]+)",\s*"name":\s*"([^"]+)",\s*"__isEntity":\s*"User",\s*"url":\s*([^,}]+)[^}]*}\s*\][^}]*"creation_time":\s*(\d+)[^}]*"message":\s*{\s*"__typename":\s*"TextWithEntities",\s*"text":\s*"([^"]+)"/g;

    // Pattern 2: Look for profile_url in the same user structure
    const profileUrlPattern = /"profile_url":\s*"([^"]+)"/g;

    // Extract profile URLs first
    const profileUrls = new Map();
    let profileMatch;
    while ((profileMatch = profileUrlPattern.exec(htmlContent)) !== null) {
      const profileUrl = profileMatch[1];
      if (profileUrl && profileUrl.includes('facebook.com') && !profileUrl.includes('login') && !profileUrl.includes('recover')) {
        // Extract user identifier from URL
        const userIdentifier = profileUrl.split('/').pop();
        profileUrls.set(userIdentifier, profileUrl);
      }
    }

    // Pattern 2: More flexible pattern for the actual Facebook structure found
    const facebookReviewPattern2 = /"actors":\s*\[\s*{\s*"__typename":\s*"User"[^}]*"id":\s*"([^"]+)"[^}]*"name":\s*"([^"]+)"[^}]*}\s*\][^}]*"creation_time":\s*(\d+)[^}]*"text":\s*"([^"]+)"/g;

    let match;
    // Try the specific pattern first
    while ((match = facebookReviewPattern1.exec(htmlContent)) !== null) {
      const userId = match[1];
      const userName = match[2];
      const userUrl = match[3] === 'null' ? null : match[3].replace(/"/g, '');
      const creationTime = parseInt(match[4]);
      const messageText = this.decodeUnicodeText(match[5]);

      // Skip if empty name or duplicate
      if (!userName || userName.trim().length === 0) continue;

      // Create unique key for duplicate detection
      const reviewKey = `${userName}_${creationTime}_${messageText.substring(0, 50)}`;
      if (seenReviews.has(reviewKey)) continue;
      seenReviews.add(reviewKey);

      // Try to find actual profile URL
      let actualProfileUrl = userUrl;
      if (!actualProfileUrl || actualProfileUrl === 'null') {
        // Look for profile URL by user ID or name
        for (const [identifier, url] of profileUrls) {
          if (identifier.includes(userId) || url.toLowerCase().includes(userName.toLowerCase().replace(/\s+/g, ''))) {
            actualProfileUrl = url;
            break;
          }
        }
      }

      // Determine if it's a recommendation
      const isRecommended = this.isRecommended('', messageText);

      reviews.push({
        author_name: userName.trim(),
        author_url: actualProfileUrl || `https://facebook.com/profile.php?id=${userId}`,
        author_id: userId,
        rating: isRecommended ? '5' : '3',
        time_posted: new Date(creationTime * 1000).toISOString(),
        time_posted_unix: creationTime,
        review_text: messageText,
        recommended: isRecommended,
        source: 'facebook_json'
      });
    }

    // Try the more flexible pattern if the first one didn't work
    if (reviews.length === 0) {
      while ((match = facebookReviewPattern2.exec(htmlContent)) !== null) {
        const userId = match[1];
        const userName = match[2];
        const creationTime = parseInt(match[3]);
        const messageText = this.decodeUnicodeText(match[4]);

        // Skip if empty name or duplicate
        if (!userName || userName.trim().length === 0) continue;

        // Create unique key for duplicate detection
        const reviewKey = `${userName}_${creationTime}_${messageText.substring(0, 50)}`;
        if (seenReviews.has(reviewKey)) continue;
        seenReviews.add(reviewKey);

        // Try to find actual profile URL
        let actualProfileUrl = null;
        for (const [identifier, url] of profileUrls) {
          if (identifier.includes(userId) || url.toLowerCase().includes(userName.toLowerCase().replace(/\s+/g, ''))) {
            actualProfileUrl = url;
            break;
          }
        }

        // Determine if it's a recommendation
        const isRecommended = this.isRecommended('', messageText);

        reviews.push({
          author_name: userName.trim(),
          author_url: actualProfileUrl || `https://facebook.com/profile.php?id=${userId}`,
          author_id: userId,
          rating: isRecommended ? '5' : '3',
          time_posted: new Date(creationTime * 1000).toISOString(),
          time_posted_unix: creationTime,
          review_text: messageText,
          recommended: isRecommended,
          source: 'facebook_json_flexible'
        });
      }
    }

    // If no specific pattern matches, fall back to general extraction but filter duplicates
    if (reviews.length === 0) {
      const generalReviews = this.extractReviews(htmlContent);
      return this.filterDuplicateReviews(generalReviews);
    }

    return reviews;
  }

  /**
   * Filter duplicate reviews based on author name and review text
   * @param {Array} reviews - Array of review objects
   * @returns {Array} Filtered array without duplicates
   */
  static filterDuplicateReviews(reviews) {
    const seen = new Set();
    return reviews.filter(review => {
      // Skip reviews with empty names
      if (!review.author_name || review.author_name.trim().length === 0) {
        return false;
      }

      // Create unique key
      const key = `${review.author_name.trim()}_${review.review_text?.substring(0, 50) || ''}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Convert Unix timestamp to readable date
   * @param {number|string} timestamp - Unix timestamp
   * @returns {string} Readable date string
   */
  static formatTimestamp(timestamp) {
    try {
      const date = new Date(parseInt(timestamp) * 1000);
      return date.toLocaleString();
    } catch (error) {
      return timestamp.toString();
    }
  }
}

// Export for use in other modules
export default FacebookReviewRegex;
