//ts-check
import puppeteer from 'puppeteer';

async function testScrolling() {
  let browser = null;

  try {
    // Launch Puppeteer browser
    browser = await puppeteer.launch({
      headless: false, // Set to false for debugging
      args: ['--no-sandbox', '--disable-setupid-sandbox'],
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();
    
    console.log('Navigating to a test page...');
    
    // Use a simple test page instead of Facebook
    await page.goto('https://example.com', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    console.log('Page loaded, starting scroll test...');
    
    // Test the page.evaluate scrolling function
    await page.evaluate(async () => {
      await new Promise((resolve) => {
        const distance = 400; // px each scroll
        const interval = 200; // ms between scrolls
        const duration = 3000; // total time: 3 seconds (shorter for testing)

        const startTime = Date.now();
        let scrollCount = 0;
        
        console.log('Starting scroll test in browser context...');
        
        const timer = setInterval(() => {
          window.scrollBy(0, distance);
          scrollCount++;
          
          console.log(`Scroll ${scrollCount}: ${Date.now() - startTime}ms elapsed`);

          if (Date.now() - startTime > duration) {
            clearInterval(timer);
            console.log(`Scrolling complete. Total scrolls: ${scrollCount}`);
            resolve();
          }
        }, interval);
      });
    });
    
    console.log('Scroll test completed successfully!');
    
    const htmlContent = await page.content();
    console.log('HTML content length:', htmlContent.length);
    
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    if (browser !== null) {
      await browser.close();
    }
  }
}

await testScrolling();
