//ts-check
import puppeteer from 'puppeteer';

async function getLatestUrl(placeId) {
  let browser = null;
  let data = { match: [] };

  try {
    // Launch Puppeteer browser
    browser = await puppeteer.launch({
      // args: ['--no-sandbox', '--disable-setupid-sandbox'],
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();

    // Go to the page where the JavaScript modifies the URL
    await page.goto('https://web.facebook.com/' + placeId + '/reviews', { waitUntil: 'domcontentloaded' }); // Use the Place ID to construct the URL

    await page.waitForSelector('[aria-label="Reviews information"]');

    // Scroll for 10 seconds
    // await page.evaluate(async () => {
    //   await new Promise((resolve) => {
    //     const distance = 400; // px each scroll
    //     const interval = 200; // ms between scrolls
    //     const duration = 10000; // total time: 10 seconds

    //     const startTime = Date.now();
    //     const timer = setInterval(() => {
    //       window.scrollBy(0, distance);

    //       if (Date.now() - startTime > duration) {
    //         clearInterval(timer);
    //         resolve();
    //       }
    //     }, interval);
    //   });
    // });

    // page.locator('body').scroll({ scrollTop: 2000 });

    // await page.waitForTimeout(10000);

    const htmlContent = await page.content();

    // Regex match the HTML
    const regex = /\\"],null,null,null,([0-9.]*?),([0-9]*?)],null,null,\[\\/;

    // Match the regex
    match = htmlContent.match(regex);

    data = { match };
  } catch (error) {
    console.error(error);
  } finally {
    if (browser !== null) {
      await browser.close();
    }
  }

  return data;
}

async function test(placeId) {
  try {
    const { match } = await getLatestUrl(placeId);
    let reviews = [];
    let rating = 0;
    let user_ratings_total = 0;

    if (match !== null) {
      rating = match[1];
      user_ratings_total = match[2];

      reviews =
        scrapedReviews
          .map((item) => {
            const review = item[0]; // Access the review section
            if (review && review.length > 0) {
              const authorName = review[1][4][5][0]; // Author name
              const authorUrl = review[1][4][2][0]; // Author URL
              const profilePhotoUrl = review[1][4][5][1]; // Profile photo URL
              const rating = review[2][0][0]; // Rating
              const relativeTimeDescription = review[1][6]; // Relative time description
              const text = review[2][15]?.[0]?.[0] || ''; // Review text
              const time = parseInt(review[1][3] / 1000000); // Time
              const language = review[2][14]?.[0] || 'en'; // Language
              const originalLanguage = review[2][14]?.[0] || 'en'; // Original Language (assuming it's the same as language)

              return {
                author_name: authorName,
                author_url: authorUrl,
                profile_photo_url: profilePhotoUrl,
                rating: rating,
                relative_time_description: relativeTimeDescription,
                text: text,
                time: time,
                language: language,
                original_language: originalLanguage,
                translated: false, // Assuming no translation in this case
              };
            }
          })
          .filter(Boolean) || []; // Remove any null or undefined entries

      // Remove reviews with no text and max 6 reviews
      reviews = reviews.filter((review) => review?.text?.length > 0).slice(0, 6);
    }

    const result = { business_status: 'OPERATIONAL', formatted_phone_number: '', name: '', opening_hours: { periods: [] }, rating, reviews, user_ratings_total };

    const review = {
      html_attributes: [],
      result,
      status: 'OK',
    };
  } catch (error) {
    console.error(error);
    return { status: 500, html_attributes: [], result: [] };
  }
}

await test('MUNAWARBOOKSTORE');
