//ts-check
import puppeteer from 'puppeteer';

async function getLatestUrl(placeId) {
  let browser = null;
  let data = { match: [] };

  try {
    // Launch Puppeteer browser
    browser = await puppeteer.launch({
      headless: false, // Set to false for debugging
      args: ['--no-sandbox', '--disable-setupid-sandbox', '--disable-web-security'],
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();

    // Set a longer timeout for navigation
    page.setDefaultNavigationTimeout(60000);

    // Set user agent to avoid detection
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    console.log(`Navigating to: https://web.facebook.com/${placeId}/reviews`);

    // Go to the page where the JavaScript modifies the URL
    await page.goto('https://web.facebook.com/' + placeId + '/reviews', {
      waitUntil: 'networkidle2',
      timeout: 60000
    }); // Use the Place ID to construct the URL

    console.log('Page loaded, waiting for reviews section...');

    try {
      await page.waitForSelector('[aria-label="Reviews information"]', { timeout: 30000 });
      console.log('Reviews section found!');
    } catch (error) {
      console.log('Reviews section not found, trying alternative selectors...');
      // Try alternative selectors if the main one fails
      const selectors = [
        '[data-testid="reviews"]',
        '[role="main"]',
        'body'
      ];

      for (const selector of selectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          console.log(`Found alternative selector: ${selector}`);
          break;
        } catch (e) {
          console.log(`Selector ${selector} not found`);
        }
      }
    }

    console.log('Starting to scroll for 10 seconds...');

    // Scroll for 10 seconds
    await page.evaluate(async () => {
      await new Promise((resolve) => {
        const distance = 400; // px each scroll
        const interval = 200; // ms between scrolls
        const duration = 10000; // total time: 10 seconds

        const startTime = Date.now();
        let scrollCount = 0;

        const timer = setInterval(() => {
          window.scrollBy(0, distance);
          scrollCount++;

          // Log progress every 2 seconds
          if (scrollCount % 10 === 0) {
            console.log(`Scrolled ${scrollCount} times, ${Date.now() - startTime}ms elapsed`);
          }

          if (Date.now() - startTime > duration) {
            clearInterval(timer);
            console.log(`Scrolling complete. Total scrolls: ${scrollCount}`);
            resolve();
          }
        }, interval);
      });
    });

    console.log('Scrolling finished, extracting content...');

    // page.locator('body').scroll({ scrollTop: 2000 });

    // await page.waitForTimeout(10000);

    const htmlContent = await page.content();

    // Regex match the HTML
    const regex = /\\"],null,null,null,([0-9.]*?),([0-9]*?)],null,null,\[\\/;

    // Match the regex
    const match = htmlContent.match(regex);

    data = { match };
  } catch (error) {
    console.error(error);
  } finally {
    if (browser !== null) {
      await browser.close();
    }
  }

  return data;
}

async function test(placeId) {
  try {
    const { match } = await getLatestUrl(placeId);
    let reviews = [];
    let rating = 0;
    let user_ratings_total = 0;

    if (match !== null) {
      rating = match[1];
      user_ratings_total = match[2];

      // For now, initialize scrapedReviews as empty array since it's not defined
      // This should be extracted from the HTML content in a real implementation
      const scrapedReviews = [];

      reviews =
        scrapedReviews
          .map((item) => {
            const review = item[0]; // Access the review section
            if (review && review.length > 0) {
              const authorName = review[1][4][5][0]; // Author name
              const authorUrl = review[1][4][2][0]; // Author URL
              const profilePhotoUrl = review[1][4][5][1]; // Profile photo URL
              const rating = review[2][0][0]; // Rating
              const relativeTimeDescription = review[1][6]; // Relative time description
              const text = review[2][15]?.[0]?.[0] || ''; // Review text
              const time = parseInt(review[1][3] / 1000000); // Time
              const language = review[2][14]?.[0] || 'en'; // Language
              const originalLanguage = review[2][14]?.[0] || 'en'; // Original Language (assuming it's the same as language)

              return {
                author_name: authorName,
                author_url: authorUrl,
                profile_photo_url: profilePhotoUrl,
                rating: rating,
                relative_time_description: relativeTimeDescription,
                text: text,
                time: time,
                language: language,
                original_language: originalLanguage,
                translated: false, // Assuming no translation in this case
              };
            }
          })
          .filter(Boolean) || []; // Remove any null or undefined entries

      // Remove reviews with no text and max 6 reviews
      reviews = reviews.filter((review) => review?.text?.length > 0).slice(0, 6);
    }

    const result = { business_status: 'OPERATIONAL', formatted_phone_number: '', name: '', opening_hours: { periods: [] }, rating, reviews, user_ratings_total };

    const response = {
      html_attributes: [],
      result,
      status: 'OK',
    };

    console.log('Test result:', response);
    return response;
  } catch (error) {
    console.error(error);
    return { status: 500, html_attributes: [], result: [] };
  }
}

await test('MUNAWARBOOKSTORE');
