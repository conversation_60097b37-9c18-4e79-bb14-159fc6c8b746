//ts-check
import puppeteer from 'puppeteer';
import FacebookReviewRegex from './facebook-review-regex.js';

async function getLatestUrl(placeId) {
  let browser = null;
  let data = { match: [] };

  try {
    // Launch Puppeteer browser
    browser = await puppeteer.launch({
      // args: ['--no-sandbox', '--disable-setupid-sandbox'],
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();

    // Go to the page where the JavaScript modifies the URL
    await page.goto('https://web.facebook.com/' + placeId + '/reviews', { waitUntil: 'domcontentloaded' }); // Use the Place ID to construct the URL

    await page.waitForSelector('[aria-label="Reviews information"]');

    // Scroll for 10 seconds
    // await page.evaluate(async () => {
    //   await new Promise((resolve) => {
    //     const distance = 400; // px each scroll
    //     const interval = 200; // ms between scrolls
    //     const duration = 10000; // total time: 10 seconds

    //     const startTime = Date.now();
    //     const timer = setInterval(() => {
    //       window.scrollBy(0, distance);

    //       if (Date.now() - startTime > duration) {
    //         clearInterval(timer);
    //         resolve();
    //       }
    //     }, interval);
    //   });
    // });

    // page.locator('body').scroll({ scrollTop: 2000 });

    // await page.waitForTimeout(10000);

    const htmlContent = await page.content();
    console.log('HTML content length:', htmlContent.length);

    // Extract reviews using comprehensive regex patterns
    console.log('Extracting reviews using Facebook regex patterns...');
    const extractedReviews = FacebookReviewRegex.extractReviews(htmlContent);
    console.log(`Found ${extractedReviews.length} reviews using regex patterns`);

    // Also try the original rating extraction pattern
    const regex = /\\"],null,null,null,([0-9.]*?),([0-9]*?)],null,null,\[\\/;
    const match = htmlContent.match(regex);

    data = {
      match,
      extractedReviews,
      htmlLength: htmlContent.length
    };
  } catch (error) {
    console.error(error);
  } finally {
    if (browser !== null) {
      await browser.close();
    }
  }

  return data;
}

async function test(placeId) {
  try {
    const { match, extractedReviews, htmlLength } = await getLatestUrl(placeId);
    let reviews = [];
    let rating = 0;
    let user_ratings_total = 0;

    console.log(`\n=== Processing Results for ${placeId} ===`);
    console.log(`HTML content length: ${htmlLength}`);
    console.log(`Extracted reviews count: ${extractedReviews?.length || 0}`);

    // Use extracted reviews from regex patterns
    if (extractedReviews && extractedReviews.length > 0) {
      console.log('Using regex-extracted reviews...');

      reviews = extractedReviews.map((review) => {
        // Clean and normalize the extracted data
        const cleanedReview = {
          author_name: FacebookReviewRegex.cleanText(review.author_name) || 'Anonymous',
          author_url: review.author_url || '',
          profile_photo_url: '', // Not extracted by regex patterns
          rating: parseFloat(review.rating) || 0,
          relative_time_description: FacebookReviewRegex.cleanText(review.time_posted) || '',
          text: FacebookReviewRegex.cleanText(review.review_text) || '',
          time: Date.now(), // Current timestamp as fallback
          language: 'en', // Default language
          original_language: 'en',
          translated: false,
          recommended: review.recommended
        };

        return cleanedReview;
      }).filter(review => review.text.length > 0); // Only include reviews with text

      // Calculate average rating from extracted reviews
      const validRatings = reviews.filter(r => r.rating > 0).map(r => r.rating);
      if (validRatings.length > 0) {
        rating = (validRatings.reduce((sum, r) => sum + r, 0) / validRatings.length).toFixed(1);
      }

      user_ratings_total = reviews.length;

      console.log(`Processed ${reviews.length} reviews with text`);
      console.log(`Average rating: ${rating}`);

    } else if (match !== null) {
      // Fallback to original extraction method
      console.log('Using fallback extraction method...');
      rating = match[1];
      user_ratings_total = match[2];
      reviews = []; // No review details available with this method
    }

    // Limit to 6 reviews maximum
    reviews = reviews.slice(0, 6);

    const result = {
      business_status: 'OPERATIONAL',
      formatted_phone_number: '',
      name: placeId,
      opening_hours: { periods: [] },
      rating,
      reviews,
      user_ratings_total
    };

    const response = {
      html_attributes: [],
      result,
      status: 'OK',
    };

    console.log('\n=== Final Results ===');
    console.log(`Business: ${placeId}`);
    console.log(`Rating: ${rating}`);
    console.log(`Total reviews: ${user_ratings_total}`);
    console.log(`Reviews with text: ${reviews.length}`);

    if (reviews.length > 0) {
      console.log('\n=== Sample Reviews ===');
      reviews.slice(0, 2).forEach((review, index) => {
        console.log(`\nReview ${index + 1}:`);
        console.log(`  Author: ${review.author_name}`);
        console.log(`  Rating: ${review.rating}`);
        console.log(`  Time: ${review.relative_time_description}`);
        console.log(`  Text: ${review.text.substring(0, 100)}${review.text.length > 100 ? '...' : ''}`);
        console.log(`  Recommended: ${review.recommended}`);
      });
    }

    return response;
  } catch (error) {
    console.error('Error in test function:', error);
    return { status: 500, html_attributes: [], result: [] };
  }
}

await test('MUNAWARBOOKSTORE');
