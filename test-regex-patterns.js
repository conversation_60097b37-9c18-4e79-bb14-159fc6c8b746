//ts-check
import FacebookReviewRegex from './facebook-review-regex.js';

// Sample Facebook HTML content for testing
const sampleFacebookHTML = `
<div class="review-container">
  <div class="review-item">
    <a href="https://www.facebook.com/john.doe.123" aria-label="<PERSON> reviewed this business"><PERSON></a>
    <div class="rating">5 out of 5 stars</div>
    <div class="time">2 days ago</div>
    <p class="review-text">Great service and friendly staff! Highly recommend this place.</p>
  </div>
  
  <div class="review-item">
    <a href="https://www.facebook.com/profile.php?id=100001234567890"><PERSON></a>
    <span aria-label="4 out of 5 stars">★★★★☆</span>
    <span data-time="1 week ago">1 week ago</span>
    <span>Good experience overall, but could be better.</span>
  </div>
  
  <div class="review-item" data-testid="review-content">
    <div><PERSON></div>
    <div>⭐⭐⭐⭐⭐</div>
    <div>3 months ago</div>
    <div>Excellent customer service and quality products!</div>
  </div>
</div>

<script type="application/json">
{
  "reviews": [
    {
      "author_name": "<PERSON>",
      "rating": 4.5,
      "review_text": "Amazing experience! Will definitely come back.",
      "created_time": "2024-01-15T10:30:00Z",
      "profile_id": "987654321"
    }
  ]
}
</script>

<div class="fb-review">
  <span>Tom Brown</span> recommends this business
  <div>Posted 5 hours ago</div>
  <p>Not recommended. Poor service and overpriced.</p>
</div>
`;

// Additional sample with Facebook's internal data structure
const sampleFacebookInternalData = `
[null,[null,null,null,[null,null,null,1642678800],null,[null,null,null,null,null,["Alice Cooper","https://facebook.com/alice.cooper",null,null,"https://scontent.xx.fbcdn.net/profile.jpg"]],["Great place!",null,null,null,5,123456]],null,null,[]]

"author_name":"Bob Dylan","rating":4,"review_text":"Good food and atmosphere","time":1642765200

[["Charlie Brown",null,null,[null,null,null,1642851600],null,[null,null,null,null,null,["Charlie Brown"]],["Decent service",null,null,null,3,789012]]]
`;

function testRegexPatterns() {
  console.log('=== Testing Facebook Review Regex Patterns ===\n');
  
  // Test with sample HTML
  console.log('1. Testing with sample HTML content:');
  console.log('=====================================');
  
  const htmlReviews = FacebookReviewRegex.extractReviews(sampleFacebookHTML);
  console.log(`Found ${htmlReviews.length} reviews from HTML:`);
  
  htmlReviews.forEach((review, index) => {
    console.log(`\nReview ${index + 1}:`);
    console.log(`  Author: ${review.author_name}`);
    console.log(`  Rating: ${review.rating}`);
    console.log(`  Time: ${review.time_posted}`);
    console.log(`  Text: ${review.review_text}`);
    console.log(`  Recommended: ${review.recommended}`);
    if (review.author_url) {
      console.log(`  Profile URL: ${review.author_url}`);
    }
  });
  
  // Test individual patterns
  console.log('\n\n2. Testing individual pattern extraction:');
  console.log('==========================================');
  
  const testContent = sampleFacebookHTML + sampleFacebookInternalData;
  
  console.log('\nReviewer Names:');
  const names = FacebookReviewRegex.extractPattern(testContent, FacebookReviewRegex.getReviewerNameRegex());
  names.forEach((name, i) => console.log(`  ${i + 1}. ${name}`));
  
  console.log('\nProfile URIs:');
  const uris = FacebookReviewRegex.extractPattern(testContent, FacebookReviewRegex.getProfileUriRegex());
  uris.forEach((uri, i) => console.log(`  ${i + 1}. ${uri}`));
  
  console.log('\nRatings:');
  const ratings = FacebookReviewRegex.extractPattern(testContent, FacebookReviewRegex.getRatingRegex());
  ratings.forEach((rating, i) => console.log(`  ${i + 1}. ${rating}`));
  
  console.log('\nTime Posted:');
  const times = FacebookReviewRegex.extractPattern(testContent, FacebookReviewRegex.getTimePostedRegex());
  times.forEach((time, i) => console.log(`  ${i + 1}. ${time}`));
  
  console.log('\nReview Texts:');
  const texts = FacebookReviewRegex.extractPattern(testContent, FacebookReviewRegex.getReviewTextRegex());
  texts.forEach((text, i) => console.log(`  ${i + 1}. ${FacebookReviewRegex.cleanText(text)}`));
  
  // Test recommendation detection
  console.log('\n\n3. Testing recommendation detection:');
  console.log('====================================');
  
  const testCases = [
    { rating: '5', text: 'Great service!' },
    { rating: '1', text: 'Terrible experience' },
    { rating: '3', text: 'Average place' },
    { rating: '', text: 'I highly recommend this place!' },
    { rating: '', text: 'Do not recommend at all' },
    { rating: '4.5', text: 'Amazing food and service' }
  ];
  
  testCases.forEach((testCase, i) => {
    const recommended = FacebookReviewRegex.isRecommended(testCase.rating, testCase.text);
    console.log(`  ${i + 1}. Rating: "${testCase.rating}", Text: "${testCase.text}" → Recommended: ${recommended}`);
  });
  
  // Test text cleaning
  console.log('\n\n4. Testing text cleaning:');
  console.log('=========================');
  
  const dirtyTexts = [
    'This is a\\ngreat place with\\n\\nexcellent service!',
    'Amazing food \\"best in town\\" according to locals',
    'Good    service   with   multiple    spaces',
    '   Leading and trailing spaces   '
  ];
  
  dirtyTexts.forEach((text, i) => {
    const cleaned = FacebookReviewRegex.cleanText(text);
    console.log(`  ${i + 1}. Original: "${text}"`);
    console.log(`     Cleaned:  "${cleaned}"`);
  });
  
  console.log('\n=== Test Complete ===');
}

// Run the tests
testRegexPatterns();
