//ts-check
import FacebookReviewRegex from './facebook-review-regex.js';
import fs from 'fs';

async function testImprovedFacebookRegex() {
  console.log('=== Testing Improved Facebook Review Extraction ===\n');
  
  try {
    // Read the actual Facebook HTML file
    const htmlContent = await fs.promises.readFile('Fetched Facebook Review Page.html', 'utf8');
    console.log(`Loaded HTML file: ${htmlContent.length} characters`);
    
    // Test the improved Facebook-specific extraction method
    console.log('\n1. Using improved extractFacebookReviews() method:');
    console.log('==================================================');
    
    const facebookReviews = FacebookReviewRegex.extractFacebookReviews(htmlContent);
    console.log(`Found ${facebookReviews.length} unique Facebook reviews (after filtering duplicates and empty names)`);
    
    if (facebookReviews.length > 0) {
      console.log('\nDetailed Facebook Reviews (No Duplicates):');
      facebookReviews.forEach((review, index) => {
        console.log(`\n--- Review ${index + 1} ---`);
        console.log(`Author Name: "${review.author_name}"`);
        console.log(`Author URL: ${review.author_url}`);
        console.log(`Author ID: ${review.author_id}`);
        console.log(`Rating: ${review.rating}`);
        console.log(`Recommended: ${review.recommended}`);
        console.log(`Time Posted: ${review.time_posted}`);
        console.log(`Time (Unix): ${review.time_posted_unix}`);
        console.log(`Time (Readable): ${FacebookReviewRegex.formatTimestamp(review.time_posted_unix)}`);
        console.log(`Review Text: ${review.review_text.substring(0, 150)}${review.review_text.length > 150 ? '...' : ''}`);
        console.log(`Source: ${review.source}`);
      });
    }
    
    // Test duplicate filtering specifically
    console.log('\n2. Testing duplicate filtering:');
    console.log('===============================');
    
    // Create some test data with duplicates
    const testReviews = [
      { author_name: 'John Doe', review_text: 'Great service!' },
      { author_name: 'John Doe', review_text: 'Great service!' }, // Duplicate
      { author_name: '', review_text: 'Anonymous review' }, // Empty name
      { author_name: 'Jane Smith', review_text: 'Good food' },
      { author_name: 'John Doe', review_text: 'Different review' }, // Same author, different text
      { author_name: '   ', review_text: 'Whitespace name' }, // Whitespace only name
    ];
    
    const filteredTestReviews = FacebookReviewRegex.filterDuplicateReviews(testReviews);
    console.log(`Original test reviews: ${testReviews.length}`);
    console.log(`After filtering: ${filteredTestReviews.length}`);
    
    filteredTestReviews.forEach((review, index) => {
      console.log(`  ${index + 1}. "${review.author_name}" - "${review.review_text}"`);
    });
    
    // Test profile URL extraction
    console.log('\n3. Testing profile URL extraction:');
    console.log('===================================');
    
    const profileUrls = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getProfileUriRegex());
    const validProfileUrls = profileUrls.filter(url => 
      url.includes('facebook.com') && 
      !url.includes('login') && 
      !url.includes('recover') &&
      !url.includes('photo') &&
      !url.includes('followers')
    );
    
    console.log(`Total URLs found: ${profileUrls.length}`);
    console.log(`Valid profile URLs: ${validProfileUrls.length}`);
    
    validProfileUrls.slice(0, 5).forEach((url, index) => {
      console.log(`  ${index + 1}. ${url}`);
    });
    
    // Test name validation
    console.log('\n4. Testing name validation:');
    console.log('===========================');
    
    const allNames = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewerNameRegex());
    const validNames = allNames.filter(name => FacebookReviewRegex.isValidReviewerName(name));
    
    console.log(`Total names found: ${allNames.length}`);
    console.log(`Valid names after filtering: ${validNames.length}`);
    
    // Show some examples of filtered out names
    const invalidNames = allNames.filter(name => !FacebookReviewRegex.isValidReviewerName(name));
    console.log(`\nExamples of filtered out names (first 5):`);
    invalidNames.slice(0, 5).forEach((name, index) => {
      console.log(`  ${index + 1}. "${name}"`);
    });
    
    console.log(`\nValid names (first 10):`);
    validNames.slice(0, 10).forEach((name, index) => {
      console.log(`  ${index + 1}. "${name}"`);
    });
    
    // Compare with general extraction method
    console.log('\n5. Comparing extraction methods:');
    console.log('================================');
    
    const generalReviews = FacebookReviewRegex.extractReviews(htmlContent);
    console.log(`General method (with filtering): ${generalReviews.length} reviews`);
    console.log(`Facebook-specific method: ${facebookReviews.length} reviews`);
    
    // Check for unique authors
    const uniqueAuthors = new Set();
    facebookReviews.forEach(review => {
      if (review.author_name && review.author_name.trim()) {
        uniqueAuthors.add(review.author_name.trim());
      }
    });
    
    console.log(`Unique authors found: ${uniqueAuthors.size}`);
    console.log(`Authors: <AUTHORS>
    
    // Final summary with quality metrics
    console.log('\n6. Quality Metrics:');
    console.log('===================');
    
    const reviewsWithNames = facebookReviews.filter(r => r.author_name && r.author_name.trim());
    const reviewsWithUrls = facebookReviews.filter(r => r.author_url && r.author_url !== 'null');
    const reviewsWithText = facebookReviews.filter(r => r.review_text && r.review_text.length > 10);
    const reviewsWithTimestamps = facebookReviews.filter(r => r.time_posted_unix);
    
    console.log(`✓ Reviews with valid names: ${reviewsWithNames.length}/${facebookReviews.length} (${Math.round(reviewsWithNames.length/facebookReviews.length*100)}%)`);
    console.log(`✓ Reviews with profile URLs: ${reviewsWithUrls.length}/${facebookReviews.length} (${Math.round(reviewsWithUrls.length/facebookReviews.length*100)}%)`);
    console.log(`✓ Reviews with meaningful text: ${reviewsWithText.length}/${facebookReviews.length} (${Math.round(reviewsWithText.length/facebookReviews.length*100)}%)`);
    console.log(`✓ Reviews with timestamps: ${reviewsWithTimestamps.length}/${facebookReviews.length} (${Math.round(reviewsWithTimestamps.length/facebookReviews.length*100)}%)`);
    console.log(`✓ No duplicate reviews: ${facebookReviews.length === uniqueAuthors.size ? 'Yes' : 'No'}`);
    console.log(`✓ No empty names: ${facebookReviews.every(r => r.author_name && r.author_name.trim()) ? 'Yes' : 'No'}`);
    
    // Sample JSON output
    console.log('\n7. Sample JSON output:');
    console.log('======================');
    
    const sampleOutput = {
      status: 'SUCCESS',
      total_reviews: facebookReviews.length,
      unique_authors: uniqueAuthors.size,
      quality_metrics: {
        reviews_with_names: reviewsWithNames.length,
        reviews_with_urls: reviewsWithUrls.length,
        reviews_with_text: reviewsWithText.length,
        reviews_with_timestamps: reviewsWithTimestamps.length
      },
      reviews: facebookReviews.slice(0, 2).map(review => ({
        author_name: review.author_name,
        author_url: review.author_url,
        author_id: review.author_id,
        rating: parseFloat(review.rating),
        time_posted: review.time_posted,
        review_text: review.review_text,
        recommended: review.recommended,
        language: /[\u0600-\u06FF]/.test(review.review_text) ? 'ar' : 'en'
      }))
    };
    
    console.log(JSON.stringify(sampleOutput, null, 2));
    
  } catch (error) {
    console.error('Error testing improved Facebook regex:', error);
  }
}

// Run the test
await testImprovedFacebookRegex();
