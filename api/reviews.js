//ts-check

import { scraper } from 'google-maps-review-scraper';
import chromium from '@sparticuz/chromium';
import puppeteer from 'puppeteer-core';

async function getMapsData(placeId) {
	let url = null;
	let browser = null;
	let data = { match: [], url: '' };

	try {
		// Launch Puppeteer browser
		browser = await puppeteer.launch({
			args: chromium.args,
			defaultViewport: chromium.defaultViewport,
			executablePath: await chromium.executablePath(),
			headless: chromium.headless,
			ignoreHTTPSErrors: true,
		});

		const page = await browser.newPage();

		// Go to the page where the JavaScript modifies the URL
		await page.goto('https://www.google.com/maps/place/?q=place_id:' + placeId, { waitUntil: 'domcontentloaded' }); // Use the Place ID to construct the URL

		const htmlContent = await page.content();

		let regex = /"https:\/\/www\.google\.com\/maps\/preview\/place\/(.*?)"/gm;

		// Match the regex
		let match = regex.exec(htmlContent);

		let url = match[1].replace(/\\/g, '').replace('u003d', '=');

		url = decodeURIComponent(url);

		url = 'https://www.google.com/maps/place/' + url + '!8m2';

		// Regex match the HTML
		regex = /\\"],null,null,null,([0-9.]*?),([0-9]*?)],null,null,\[\\/;

		// Match the regex
		match = htmlContent.match(regex);

		data = { match, url };
	} catch (error) {
		console.error(error);
	} finally {
		if (browser !== null) {
			await browser.close();
		}
	}

	return data;
}

export default async function handler(req, res) {
	const { placeId } = req.query; // Get the placeId from the query parameter

	if (!placeId) {
		return res.status(400).json({ error: 'Place ID is required' });
	}

	try {
		const { match, url } = await getMapsData(placeId);
		let result = { business_status: 'OPERATIONAL', formatted_phone_number: '', name: '', opening_hours: { periods: [] } };

		if (!url) {
			return res.status(500).json({ error: 'Failed to retrieve the URL' });
		}

		if (match !== null) {
			const rating = match[1];
			const user_ratings_total = match[2];

			// Scrape reviews using the scraper
			const scrapedReviews = await scraper(url, { sort_type: 'newest', search_query: '', pages: '1' });

			let reviews =
				scrapedReviews
					.map((item) => {
						const review = item[0]; // Access the review section
						if (review && review.length > 0) {
							const authorName = review[1][4][5][0]; // Author name
							const authorUrl = review[1][4][2][0]; // Author URL
							const profilePhotoUrl = review[1][4][5][1]; // Profile photo URL
							const rating = review[2][0][0]; // Rating
							const relativeTimeDescription = review[1][6]; // Relative time description
							const text = review[2][15]?.[0]?.[0] || ''; // Review text
							const time = parseInt(review[1][3] / 1000000); // Time
							const language = review[2][14]?.[0] || 'en'; // Language
							const originalLanguage = review[2][14]?.[0] || 'en'; // Original Language (assuming it's the same as language)

							return {
								author_name: authorName,
								author_url: authorUrl,
								profile_photo_url: profilePhotoUrl,
								rating: rating,
								relative_time_description: relativeTimeDescription,
								text: text,
								time: time,
								language: language,
								original_language: originalLanguage,
								translated: false, // Assuming no translation in this case
							};
						}
					})
					.filter(Boolean) || []; // Remove any null or undefined entries

			// Remove reviews with no text and max 6 reviews
			reviews = reviews.filter((review) => review?.text?.length > 0).slice(0, 6);

			result = { ...result, rating, reviews, user_ratings_total };
		}

		const review = {
			html_attributes: [],
			result,
			status: 'OK',
		};

		return res.status(200).json(review);
	} catch (error) {
		console.error(error);
		return { status: 500, html_attributes: [], result: [] };
	}
}
