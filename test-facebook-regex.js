//ts-check
import puppeteer from 'puppeteer';
import FacebookReviewRegex from './facebook-review-regex.js';

async function testFacebookReviewExtraction(placeId) {
  let browser = null;
  let extractedReviews = [];

  try {
    // Launch Puppeteer browser
    browser = await puppeteer.launch({
      headless: false, // Set to false for debugging
      args: ['--no-sandbox', '--disable-setupid-sandbox', '--disable-web-security'],
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();
    
    // Set a longer timeout for navigation
    page.setDefaultNavigationTimeout(60000);
    
    // Set user agent to avoid detection
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    console.log(`Navigating to: https://web.facebook.com/${placeId}/reviews`);
    
    try {
      // Go to the Facebook page
      await page.goto('https://web.facebook.com/' + placeId + '/reviews', { 
        waitUntil: 'networkidle2',
        timeout: 60000 
      });

      console.log('Page loaded, waiting for content...');
      
      // Wait for any content to load
      await page.waitForTimeout(5000);
      
      // Try to find reviews section with multiple selectors
      const selectors = [
        '[aria-label="Reviews information"]',
        '[data-testid="reviews"]',
        '[role="main"]',
        'body'
      ];
      
      let contentFound = false;
      for (const selector of selectors) {
        try {
          await page.waitForSelector(selector, { timeout: 5000 });
          console.log(`Found content with selector: ${selector}`);
          contentFound = true;
          break;
        } catch (e) {
          console.log(`Selector ${selector} not found`);
        }
      }

      if (contentFound) {
        console.log('Starting to scroll for content...');
        
        // Scroll to load more reviews
        await page.evaluate(async () => {
          await new Promise((resolve) => {
            const distance = 400;
            const interval = 200;
            const duration = 10000; // 10 seconds

            const startTime = Date.now();
            let scrollCount = 0;
            
            const timer = setInterval(() => {
              window.scrollBy(0, distance);
              scrollCount++;

              if (Date.now() - startTime > duration) {
                clearInterval(timer);
                console.log(`Scrolling complete. Total scrolls: ${scrollCount}`);
                resolve();
              }
            }, interval);
          });
        });
        
        console.log('Scrolling finished, extracting content...');
      }

      // Get the HTML content
      const htmlContent = await page.content();
      console.log('HTML content length:', htmlContent.length);

      // Extract reviews using our regex patterns
      console.log('Extracting reviews using regex patterns...');
      extractedReviews = FacebookReviewRegex.extractReviews(htmlContent);
      
      console.log(`Found ${extractedReviews.length} potential reviews`);

      // Also try to extract specific patterns individually for debugging
      console.log('\n=== Individual Pattern Results ===');
      
      const names = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewerNameRegex());
      console.log(`Reviewer names found: ${names.length}`, names.slice(0, 5));
      
      const ratings = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getRatingRegex());
      console.log(`Ratings found: ${ratings.length}`, ratings.slice(0, 5));
      
      const times = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getTimePostedRegex());
      console.log(`Time stamps found: ${times.length}`, times.slice(0, 5));
      
      const texts = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewTextRegex());
      console.log(`Review texts found: ${texts.length}`, texts.slice(0, 5));
      
      const uris = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getProfileUriRegex());
      console.log(`Profile URIs found: ${uris.length}`, uris.slice(0, 5));

      // Save HTML content for manual inspection
      console.log('\n=== Saving HTML for manual inspection ===');
      const fs = await import('fs');
      await fs.promises.writeFile('facebook-page-content.html', htmlContent);
      console.log('HTML content saved to facebook-page-content.html');

    } catch (navigationError) {
      console.error('Navigation failed:', navigationError.message);
      
      // Try alternative approach - search for the business on Facebook
      console.log('Trying alternative approach...');
      
      try {
        await page.goto(`https://www.facebook.com/search/top/?q=${encodeURIComponent(placeId)}`, {
          waitUntil: 'networkidle2',
          timeout: 30000
        });
        
        await page.waitForTimeout(3000);
        const searchContent = await page.content();
        
        console.log('Search page loaded, extracting any review data...');
        extractedReviews = FacebookReviewRegex.extractReviews(searchContent);
        
      } catch (searchError) {
        console.error('Search approach also failed:', searchError.message);
      }
    }

  } catch (error) {
    console.error('Error during extraction:', error);
  } finally {
    if (browser !== null) {
      await browser.close();
    }
  }

  return {
    reviews: extractedReviews,
    total_found: extractedReviews.length,
    status: extractedReviews.length > 0 ? 'SUCCESS' : 'NO_REVIEWS_FOUND'
  };
}

// Test the extraction
async function runTest() {
  console.log('=== Facebook Review Regex Test ===\n');
  
  const result = await testFacebookReviewExtraction('MUNAWARBOOKSTORE');
  
  console.log('\n=== Final Results ===');
  console.log(`Status: ${result.status}`);
  console.log(`Total reviews found: ${result.total_found}`);
  
  if (result.reviews.length > 0) {
    console.log('\n=== Sample Reviews ===');
    result.reviews.slice(0, 3).forEach((review, index) => {
      console.log(`\nReview ${index + 1}:`);
      console.log(`  Author: ${review.author_name}`);
      console.log(`  Rating: ${review.rating}`);
      console.log(`  Time: ${review.time_posted}`);
      console.log(`  Text: ${review.review_text?.substring(0, 100)}...`);
      console.log(`  Recommended: ${review.recommended}`);
      if (review.author_url) {
        console.log(`  Profile URL: ${review.author_url}`);
      }
    });
  }
  
  return result;
}

// Run the test
await runTest();
