//ts-check
import FacebookReviewRegex from './facebook-review-regex.js';
import fs from 'fs';

async function testFacebookHtmlRegex() {
  console.log('=== Testing Facebook HTML Regex Patterns ===\n');
  
  try {
    // Read the actual Facebook HTML file
    const htmlContent = await fs.promises.readFile('Fetched Facebook Review Page.html', 'utf8');
    console.log(`Loaded HTML file: ${htmlContent.length} characters`);
    
    // Extract reviews using our updated regex patterns
    console.log('\n1. Extracting reviews using updated patterns...');
    const extractedReviews = FacebookReviewRegex.extractReviews(htmlContent);
    console.log(`Found ${extractedReviews.length} reviews`);
    
    // Test individual patterns
    console.log('\n2. Testing individual pattern extraction:');
    console.log('==========================================');
    
    console.log('\nReviewer Names:');
    const names = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewerNameRegex());
    names.slice(0, 10).forEach((name, i) => {
      console.log(`  ${i + 1}. ${name}`);
    });
    console.log(`  ... and ${Math.max(0, names.length - 10)} more`);
    
    console.log('\nProfile URIs:');
    const uris = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getProfileUriRegex());
    uris.slice(0, 5).forEach((uri, i) => {
      console.log(`  ${i + 1}. ${uri}`);
    });
    console.log(`  ... and ${Math.max(0, uris.length - 5)} more`);
    
    console.log('\nTime Posted:');
    const times = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getTimePostedRegex());
    times.slice(0, 5).forEach((time, i) => {
      // Convert Unix timestamp to readable date if it's a number
      const timeDisplay = /^\d{10,13}$/.test(time) 
        ? new Date(parseInt(time) * 1000).toLocaleString() 
        : time;
      console.log(`  ${i + 1}. ${time} (${timeDisplay})`);
    });
    console.log(`  ... and ${Math.max(0, times.length - 5)} more`);
    
    console.log('\nRatings/Recommendations:');
    const ratings = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getRatingRegex());
    ratings.slice(0, 10).forEach((rating, i) => {
      console.log(`  ${i + 1}. ${rating}`);
    });
    console.log(`  ... and ${Math.max(0, ratings.length - 10)} more`);
    
    console.log('\nReview Texts:');
    const texts = FacebookReviewRegex.extractPattern(htmlContent, FacebookReviewRegex.getReviewTextRegex());
    texts.slice(0, 5).forEach((text, i) => {
      const cleanedText = FacebookReviewRegex.cleanText(text);
      const displayText = cleanedText.length > 100 
        ? cleanedText.substring(0, 100) + '...' 
        : cleanedText;
      console.log(`  ${i + 1}. ${displayText}`);
    });
    console.log(`  ... and ${Math.max(0, texts.length - 5)} more`);
    
    // Test Unicode decoding specifically
    console.log('\n3. Testing Unicode decoding:');
    console.log('=============================');
    
    const unicodePattern = /"text":\s*"([^"]*\\u[^"]+)"/g;
    let unicodeMatch;
    let unicodeCount = 0;
    
    while ((unicodeMatch = unicodePattern.exec(htmlContent)) !== null && unicodeCount < 3) {
      const originalText = unicodeMatch[1];
      const decodedText = FacebookReviewRegex.decodeUnicodeText(originalText);
      console.log(`\nUnicode text ${unicodeCount + 1}:`);
      console.log(`  Original: ${originalText.substring(0, 100)}...`);
      console.log(`  Decoded:  ${decodedText.substring(0, 100)}...`);
      unicodeCount++;
    }
    
    // Show final processed reviews
    console.log('\n4. Final processed reviews:');
    console.log('===========================');
    
    if (extractedReviews.length > 0) {
      extractedReviews.slice(0, 3).forEach((review, index) => {
        console.log(`\nReview ${index + 1}:`);
        console.log(`  Author: ${review.author_name}`);
        console.log(`  Profile URL: ${review.author_url}`);
        console.log(`  Rating/Recommendation: ${review.rating}`);
        console.log(`  Time: ${review.time_posted}`);
        console.log(`  Text: ${review.review_text?.substring(0, 150)}${review.review_text?.length > 150 ? '...' : ''}`);
        console.log(`  Recommended: ${review.recommended}`);
      });
      
      if (extractedReviews.length > 3) {
        console.log(`\n... and ${extractedReviews.length - 3} more reviews`);
      }
    } else {
      console.log('No reviews found with current patterns.');
    }
    
    // Test specific Facebook JSON patterns
    console.log('\n5. Testing Facebook-specific JSON patterns:');
    console.log('===========================================');
    
    // Look for the specific review structure we saw in the HTML
    const reviewStructurePattern = /"actors":\s*\[\s*{[^}]*"name":\s*"([^"]+)"[^}]*}[^}]*\][^}]*"message":\s*{[^}]*"text":\s*"([^"]+)"/g;
    let structureMatch;
    let structureCount = 0;
    
    while ((structureMatch = reviewStructurePattern.exec(htmlContent)) !== null && structureCount < 3) {
      const authorName = structureMatch[1];
      const messageText = FacebookReviewRegex.cleanText(structureMatch[2]);
      
      console.log(`\nFacebook JSON Review ${structureCount + 1}:`);
      console.log(`  Author: ${authorName}`);
      console.log(`  Message: ${messageText.substring(0, 100)}${messageText.length > 100 ? '...' : ''}`);
      structureCount++;
    }
    
    // Summary
    console.log('\n6. Summary:');
    console.log('===========');
    console.log(`Total names found: ${names.length}`);
    console.log(`Total URIs found: ${uris.length}`);
    console.log(`Total timestamps found: ${times.length}`);
    console.log(`Total ratings found: ${ratings.length}`);
    console.log(`Total review texts found: ${texts.length}`);
    console.log(`Total processed reviews: ${extractedReviews.length}`);
    
  } catch (error) {
    console.error('Error testing Facebook HTML regex:', error);
  }
}

// Run the test
await testFacebookHtmlRegex();
